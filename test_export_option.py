#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de test pour valider la nouvelle option --export
"""

import subprocess
import os
import csv
import sys

def run_command(command):
    """Exécute une commande et retourne le résultat"""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            capture_output=True, 
            text=True,
            cwd=os.path.dirname(os.path.abspath(__file__))
        )
        return result.returncode, result.stdout, result.stderr
    except Exception as e:
        return -1, "", str(e)

def test_export_option():
    """Test de l'option --export"""
    print("🧪 Test de l'option --export")
    print("=" * 40)
    
    # Test avec --export 50
    cmd = "python demographics_generator_parse.py --export 50 -o test_export_50.csv"
    returncode, stdout, stderr = run_command(cmd)
    
    if returncode == 0:
        print("✅ Commande --export 50 réussie")
        
        # Vérifier le fichier
        if os.path.exists("test_export_50.csv"):
            with open("test_export_50.csv", 'r', encoding='utf-8') as f:
                reader = csv.reader(f, delimiter=';')
                lines = list(reader)
                
            if len(lines) == 51:  # 50 + header
                print(f"✅ Fichier généré avec {len(lines)-1} lignes de données")
                os.remove("test_export_50.csv")
                return True
            else:
                print(f"❌ Nombre de lignes incorrect: {len(lines)-1}")
                return False
        else:
            print("❌ Fichier non créé")
            return False
    else:
        print(f"❌ Erreur dans la commande: {stderr}")
        return False

def test_retrocompatibilite():
    """Test de rétrocompatibilité avec -n"""
    print("\n🧪 Test de rétrocompatibilité (-n)")
    print("=" * 40)
    
    cmd = "python demographics_generator_parse.py -n 30 -o test_retro.csv"
    returncode, stdout, stderr = run_command(cmd)
    
    if returncode == 0:
        if "dépréciée" in stdout:
            print("✅ Message de dépréciation affiché")
        
        # Vérifier le fichier
        if os.path.exists("test_retro.csv"):
            with open("test_retro.csv", 'r', encoding='utf-8') as f:
                reader = csv.reader(f, delimiter=';')
                lines = list(reader)
                
            if len(lines) == 31:  # 30 + header
                print(f"✅ Rétrocompatibilité fonctionnelle ({len(lines)-1} lignes)")
                os.remove("test_retro.csv")
                return True
            else:
                print(f"❌ Nombre de lignes incorrect: {len(lines)-1}")
                return False
        else:
            print("❌ Fichier non créé")
            return False
    else:
        print(f"❌ Erreur dans la commande: {stderr}")
        return False

def test_erreur_sans_option():
    """Test d'erreur quand aucune option n'est fournie"""
    print("\n🧪 Test d'erreur sans option")
    print("=" * 40)
    
    cmd = "python demographics_generator_parse.py -o test_erreur.csv"
    returncode, stdout, stderr = run_command(cmd)
    
    if returncode != 0:
        if "Vous devez spécifier" in stdout or "Vous devez spécifier" in stderr:
            print("✅ Message d'erreur approprié affiché")
            return True
        else:
            print(f"❌ Message d'erreur inattendu: {stdout} {stderr}")
            return False
    else:
        print("❌ La commande aurait dû échouer")
        return False

def test_help():
    """Test de l'aide"""
    print("\n🧪 Test de l'aide")
    print("=" * 40)
    
    cmd = "python demographics_generator_parse.py --help"
    returncode, stdout, stderr = run_command(cmd)
    
    if returncode == 0:
        if "--export" in stdout and "obligatoire" not in stdout:
            print("✅ Option --export présente dans l'aide")
            if "déprécié" in stdout:
                print("✅ Mention de dépréciation pour -n présente")
                return True
            else:
                print("⚠️  Mention de dépréciation manquante")
                return True
        else:
            print("❌ Option --export manquante ou mal décrite")
            return False
    else:
        print(f"❌ Erreur dans l'aide: {stderr}")
        return False

def test_stats_avec_export():
    """Test de --stats avec --export"""
    print("\n🧪 Test de --stats avec --export")
    print("=" * 40)
    
    cmd = "python demographics_generator_parse.py --export 25 -o test_stats.csv --stats"
    returncode, stdout, stderr = run_command(cmd)
    
    if returncode == 0:
        if "Répartition par sexe" in stdout and "Prénoms composés" in stdout:
            print("✅ Statistiques générées correctement")
            if os.path.exists("test_stats.csv"):
                os.remove("test_stats.csv")
            return True
        else:
            print("❌ Statistiques manquantes")
            return False
    else:
        print(f"❌ Erreur dans la commande: {stderr}")
        return False

def main():
    """Exécute tous les tests"""
    print("🚀 Tests de la nouvelle option --export")
    print("=" * 60)
    
    tests = [
        ("Option --export", test_export_option),
        ("Rétrocompatibilité -n", test_retrocompatibilite),
        ("Erreur sans option", test_erreur_sans_option),
        ("Aide", test_help),
        ("Stats avec export", test_stats_avec_export)
    ]
    
    resultats = []
    
    for nom_test, fonction_test in tests:
        try:
            resultat = fonction_test()
            resultats.append((nom_test, resultat))
        except Exception as e:
            print(f"❌ Erreur dans {nom_test}: {e}")
            resultats.append((nom_test, False))
    
    # Résumé
    print("\n" + "=" * 60)
    print("📊 RÉSUMÉ DES TESTS")
    print("=" * 60)
    
    succes = 0
    for nom_test, resultat in resultats:
        statut = "✅ RÉUSSI" if resultat else "❌ ÉCHEC"
        print(f"   {nom_test}: {statut}")
        if resultat:
            succes += 1
    
    print(f"\n🎯 Score: {succes}/{len(tests)} tests réussis")
    
    if succes == len(tests):
        print("🎉 L'option --export fonctionne parfaitement !")
        print("\n💡 Utilisation recommandée:")
        print("   python demographics_generator_parse.py --export 500")
        print("   python demographics_generator_parse.py --export 1000 --stats")
        print("   python demographics_generator_parse.py --export 200 -o mon_fichier.csv")
    else:
        print("⚠️  Certains tests ont échoué")

if __name__ == "__main__":
    main()
