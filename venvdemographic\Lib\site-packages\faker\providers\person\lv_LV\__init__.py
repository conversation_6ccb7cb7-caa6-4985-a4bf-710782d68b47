from .. import Provider as PersonProvider


class Provider(PersonProvider):
    formats = (
        "{{first_name}} {{last_name}}",
        "{{first_name}} {{last_name}}",
        "{{last_name}}, {{first_name}}",
    )

    first_names_male = (
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "Artis",
        "Arturs",
        "<PERSON>ū<PERSON>",
        "<PERSON>r<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "Bērends",
        "<PERSON><PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON>",
        "<PERSON>ā<PERSON>",
        "Dzintars",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON>",
        "<PERSON><PERSON><PERSON>",
        "Ēriks",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "Ēvalds",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "Ģederts",
        "Ģirts",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "Indriķis",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "Krists",
        "Krišjānis",
        "Krišs",
        "Laimonis",
        "Lauris",
        "Leons",
        "Macs",
        "Mareks",
        "Māris",
        "Mārtiņš",
        "Matīss",
        "Mihels",
        "Mikels",
        "Miķelis",
        "Modris",
        "Nikolajs",
        "Niks",
        "Normunds",
        "Oļģerts",
        "Oskars",
        "Osvalds",
        "Oto",
        "Pauls",
        "Pēteris",
        "Raimonds",
        "Raivis",
        "Reinis",
        "Ričards",
        "Rihards",
        "Roberts",
        "Rolands",
        "Rūdolfs",
        "Sandis",
        "Staņislavs",
        "Tenis",
        "Teodors",
        "Toms",
        "Uldis",
        "Valdis",
        "Viesturs",
        "Viktors",
        "Vilis",
        "Vilnis",
        "Viļums",
        "Visvaldis",
        "Vladislavs",
        "Voldemārs",
        "Ziedonis",
        "Žanis",
    )

    first_names_female = (
        "Agnese",
        "Aiga",
        "Aija",
        "Aina",
        "Alīda",
        "Alise",
        "Alma",
        "Alvīne",
        "Amālija",
        "Anete",
        "Anita",
        "Anna",
        "Annija",
        "Antoņina",
        "Antra",
        "Ārija",
        "Ausma",
        "Austra",
        "Baiba",
        "Berta",
        "Biruta",
        "Broņislava",
        "Dace",
        "Daiga",
        "Daina",
        "Dārta",
        "Diāna",
        "Doroteja",
        "Dzidra",
        "Dzintra",
        "Eda",
        "Edīte",
        "Elīna",
        "Elita",
        "Elizabete",
        "Elvīra",
        "Elza",
        "Emīlija",
        "Emma",
        "Ērika",
        "Erna",
        "Eva",
        "Evija",
        "Evita",
        "Gaida",
        "Genovefa",
        "Grēta",
        "Grieta",
        "Gunita",
        "Gunta",
        "Helēna",
        "Ieva",
        "Ilga",
        "Ilona",
        "Ilze",
        "Ina",
        "Ināra",
        "Indra",
        "Inese",
        "Ineta",
        "Inga",
        "Ingrīda",
        "Inguna",
        "Inta",
        "Irēna",
        "Irma",
        "Iveta",
        "Jana",
        "Janina",
        "Jūle",
        "Jūla",
        "Jūlija",
        "Karina",
        "Karlīna",
        "Katarīna",
        "Katrīna",
        "Krista",
        "Kristiāna",
        "Laila",
        "Laura",
        "Lavīze",
        "Leontīne",
        "Lība",
        "Lidija",
        "Liene",
        "Līga",
        "Ligita",
        "Lilija",
        "Lilita",
        "Līna",
        "Linda",
        "Līza",
        "Lizete",
        "Lūcija",
        "Madara",
        "Made",
        "Maija",
        "Māra",
        "Mare",
        "Margareta",
        "Margrieta",
        "Marija",
        "Mārīte",
        "Marta",
        "Maža",
        "Milda",
        "Minna",
        "Mirdza",
        "Monika",
        "Natālija",
        "Olga",
        "Otīlija",
        "Paula",
        "Paulīna",
        "Rasma",
        "Regīna",
        "Rita",
        "Rudīte",
        "Ruta",
        "Rute",
        "Samanta",
        "Sandra",
        "Sanita",
        "Santa",
        "Sapa",
        "Sarmīte",
        "Silvija",
        "Sintija",
        "Skaidrīte",
        "Solvita",
        "Tekla",
        "Trīne",
        "Valda",
        "Valentīna",
        "Valija",
        "Velta",
        "Veneranda",
        "Vera",
        "Veronika",
        "Vija",
        "Vilma",
        "Vineta",
        "Vita",
        "Zane",
        "Zelma",
        "Zenta",
        "Zigrīda",
    )

    first_names = first_names_male + first_names_female

    last_names_nonbinary = (
        "Ābele",
        "Bite",
        "Caune",
        "Krūze",
        "Lapsa",
        "Liepa",
        "Paegle",
        "Priede",
        "Roze",
        "Skuja",
        "Vīksna",
        "Zvaigzne",
    )

    last_names_male = (
        "Āboliņš",
        "Ābols",
        "Alksnis",
        "Apinis",
        "Apsītis",
        "Auniņš",
        "Auziņš",
        "Avotiņš",
        "Balodis",
        "Baltiņš",
        "Bērziņš",
        "Birznieks",
        "Briedis",
        "Celmiņš",
        "Celms",
        "Cīrulis",
        "Dzenis",
        "Dūmiņš",
        "Eglītis",
        "Jaunzems",
        "Kalējs",
        "Kalniņš",
        "Kaņeps",
        "Kārkliņš",
        "Kauliņš",
        "Kļaviņš",
        "Krastiņš",
        "Krēsliņš",
        "Krieviņš",
        "Krievs",
        "Krūmiņš",
        "Kundziņš",
        "Lācis",
        "Lagzdiņš",
        "Līcis",
        "Liepiņš",
        "Lukstiņš",
        "Lūsis",
        "Pērkons",
        "Podnieks",
        "Polis",
        "Priedītis",
        "Puriņš",
        "Purmals",
        "Riekstiņš",
        "Rozītis",
        "Rubenis",
        "Rudzītis",
        "Saulītis",
        "Siliņš",
        "Skujiņš",
        "Sproģis",
        "Strazdiņš",
        "Turiņš",
        "Vanags",
        "Vilciņš",
        "Vilks",
        "Vītoliņš",
        "Vītols",
        "Zaķis",
        "Zālītis",
        "Zariņš",
        "Zeltiņš",
        "Ziemelis",
        "Zirnis",
        "Zvirbulis",
    )

    last_names_female = (
        "Āboliņa",
        "Ābola",
        "Alksne",
        "Apine",
        "Apsīte",
        "Auniņa",
        "Auziņa",
        "Avotiņa",
        "Balode",
        "Bērziņa",
        "Birzniece",
        "Briede",
        "Celmiņa",
        "Celma",
        "Cīrule",
        "Dzene",
        "Dūmiņa",
        "Eglīte",
        "Jaunzema",
        "Kalēja",
        "Kalniņa",
        "Kaņepa",
        "Kārkliņa",
        "Kauliņa",
        "Kļaviņa",
        "Krastiņa",
        "Krēsliņa",
        "Krieviņa",
        "Krieva",
        "Krūmiņa",
        "Kundziņa",
        "Lāce",
        "Lagzdiņa",
        "Līce",
        "Liepiņa",
        "Lukstiņa",
        "Lūse",
        "Pērkona",
        "Podniece",
        "Pole",
        "Priedīte",
        "Puriņa",
        "Purmale",
        "Riekstiņa",
        "Rozīte",
        "Rubene",
        "Rudzīte",
        "Saulīte",
        "Siliņa",
        "Skujiņa",
        "Sproģe",
        "Strazdiņa",
        "Turiņa",
        "Vanaga",
        "Vilciņa",
        "Vītoliņa",
        "Vītola",
        "Zaķe",
        "Zālīte",
        "Zariņa",
        "Zeltiņa",
        "Ziemele",
        "Zirne",
        "Zvirbule",
    )

    last_names = last_names_male + last_names_female + last_names_nonbinary
