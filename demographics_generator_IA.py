#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Générateur de démographies fictives avec SLM (Ollama)
Utilise un modèle de langage pour générer des données plus réalistes et variées
"""

import csv
import json
import random
import requests
import argparse
import sys
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import time

class OllamaClient:
    """Client pour communiquer avec Ollama"""

    def __init__(self, base_url: str = "http://localhost:11434", model: str = "llama3.2", timeout: int = 300):
        self.base_url = base_url
        self.model = model
        self.timeout = timeout
        self.session = requests.Session()
        
    def is_available(self) -> bool:
        """Vérifie si Ollama est disponible"""
        try:
            response = self.session.get(f"{self.base_url}/api/tags", timeout=10)
            return response.status_code == 200
        except:
            return False
    
    def list_models(self) -> List[str]:
        """Liste les modèles disponibles"""
        try:
            response = self.session.get(f"{self.base_url}/api/tags", timeout=10)
            if response.status_code == 200:
                models = response.json().get('models', [])
                return [model['name'].split(':')[0] for model in models]
        except:
            pass
        return []
    
    def generate(self, prompt: str, temperature: float = 0.7) -> Optional[str]:
        """Génère du texte avec le modèle"""
        try:
            payload = {
                "model": self.model,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": temperature,
                    "top_p": 0.9,
                    "max_tokens": 1000
                }
            }

            print(f"🔄 Génération avec {self.model} (timeout: {self.timeout}s)...")
            response = self.session.post(
                f"{self.base_url}/api/generate",
                json=payload,
                timeout=self.timeout
            )

            if response.status_code == 200:
                result = response.json().get('response', '').strip()
                print(f"✅ Génération réussie ({len(result)} caractères)")
                return result
            else:
                print(f"❌ Erreur HTTP {response.status_code}: {response.text}")

        except requests.exceptions.Timeout:
            print(f"⏰ Timeout après {self.timeout}s - Le modèle prend trop de temps à répondre")
            print("💡 Suggestions:")
            print("   - Utilisez un modèle plus petit (gemma2:2b)")
            print("   - Réduisez la taille des lots (--batch-size 5)")
            print("   - Augmentez le timeout avec --timeout 300")
        except requests.exceptions.ConnectionError:
            print("🔌 Erreur de connexion - Vérifiez qu'Ollama est démarré")
        except Exception as e:
            print(f"❌ Erreur Ollama: {e}")

        return None

class DemographicsGenerator:
    """Générateur de démographies avec SLM"""
    
    def __init__(self, ollama_client: OllamaClient):
        self.ollama = ollama_client
        self.batch_size = 10  # Nombre de démographies par requête LLM
        
        # Codes postaux français réels pour validation
        self.codes_postaux_villes = {
            '75001': 'Paris', '75002': 'Paris', '75003': 'Paris', '75004': 'Paris', '75005': 'Paris',
            '75006': 'Paris', '75007': 'Paris', '75008': 'Paris', '75009': 'Paris', '75010': 'Paris',
            '75011': 'Paris', '75012': 'Paris', '75013': 'Paris', '75014': 'Paris', '75015': 'Paris',
            '75016': 'Paris', '75017': 'Paris', '75018': 'Paris', '75019': 'Paris', '75020': 'Paris',
            '69001': 'Lyon', '69002': 'Lyon', '69003': 'Lyon', '69004': 'Lyon', '69005': 'Lyon',
            '69006': 'Lyon', '69007': 'Lyon', '69008': 'Lyon', '69009': 'Lyon',
            '13001': 'Marseille', '13002': 'Marseille', '13003': 'Marseille', '13004': 'Marseille',
            '13005': 'Marseille', '13006': 'Marseille', '13007': 'Marseille', '13008': 'Marseille',
            '33000': 'Bordeaux', '33100': 'Bordeaux', '33200': 'Bordeaux', '33300': 'Bordeaux',
            '31000': 'Toulouse', '31100': 'Toulouse', '31200': 'Toulouse', '31300': 'Toulouse',
            '06000': 'Nice', '06100': 'Nice', '06200': 'Nice', '59000': 'Lille', '67000': 'Strasbourg',
            '44000': 'Nantes', '35000': 'Rennes', '76000': 'Rouen', '49000': 'Angers', '37000': 'Tours',
            '21000': 'Dijon', '51100': 'Reims', '80000': 'Amiens', '87000': 'Limoges', '25000': 'Besançon',
            '38000': 'Grenoble', '42000': 'Saint-Étienne', '30000': 'Nîmes', '34000': 'Montpellier',
            '64000': 'Pau', '83000': 'Toulon', '17000': 'La Rochelle', '29200': 'Brest', '56000': 'Vannes'
        }
    
    def create_prompt(self, batch_size: int) -> str:
        """Crée le prompt pour le LLM"""
        codes_postaux_exemples = list(self.codes_postaux_villes.keys())[:20]
        
        prompt = f"""Génère exactement {batch_size} démographies de patients français fictifs et réalistes.
Chaque démographie doit être au format JSON avec ces champs exacts :

IMPORTANT : Réponds UNIQUEMENT avec un tableau JSON valide, aucun autre texte.

Format requis pour chaque patient :
{{
  "nom_naissance": "NOM en majuscules (nom de famille français typique)",
  "nom_usage": "NOM en majuscules (même que nom_naissance ou nom marital pour les femmes)",
  "prenom": "Prénom français réaliste (première lettre majuscule)",
  "date_naissance": "JJ/MM/AAAA (entre 1920 et 2023, distribution réaliste par âge)",
  "code_postal": "Code postal français à 5 chiffres (exemples: {', '.join(codes_postaux_exemples[:10])})",
  "ville": "Ville française correspondant au code postal",
  "numero_securite_sociale": "Numéro français format: X AA MM DD CCC NNN KK (X=1ou2, AA=année, MM=mois, DD=dept, CCC=commune, NNN=ordre, KK=clé)",
  "numero_ins": "8 chiffres aléatoires"
}}

Exigences spécifiques :
- Noms français authentiques et variés (Martin, Dupont, Lefebvre, Bernard, etc.)
- Prénoms cohérents avec l'âge (Jean/Marie pour âgés, Emma/Hugo pour jeunes)
- Dates de naissance réalistes (plus de personnes entre 20-70 ans)
- Numéros sécurité sociale cohérents (1=homme, 2=femme, année/mois de naissance)
- Codes postaux français valides uniquement
- Noms d'usage différents pour ~30% des femmes (effet mariage)

Réponds par un tableau JSON de {batch_size} éléments :
[
  {{ ... patient 1 ... }},
  {{ ... patient 2 ... }},
  ...
]"""
        
        return prompt
    
    def parse_llm_response(self, response: str) -> List[Dict]:
        """Parse la réponse du LLM et valide les données"""
        try:
            # Nettoyer la réponse (supprimer markdown, texte superflu)
            cleaned_response = response.strip()
            
            # Chercher le JSON dans la réponse
            json_match = re.search(r'\[.*\]', cleaned_response, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
            else:
                json_str = cleaned_response
            
            # Parser le JSON
            demographics = json.loads(json_str)
            
            if not isinstance(demographics, list):
                return []
            
            # Valider et corriger chaque démographie
            validated_demographics = []
            for demo in demographics:
                validated = self.validate_and_fix_demographic(demo)
                if validated:
                    validated_demographics.append(validated)
            
            return validated_demographics
            
        except Exception as e:
            print(f"Erreur parsing LLM: {e}")
            print(f"Réponse: {response[:200]}...")
            return []
    
    def validate_and_fix_demographic(self, demo: Dict) -> Optional[Dict]:
        """Valide et corrige une démographie"""
        try:
            # Champs requis
            required_fields = [
                'nom_naissance', 'nom_usage', 'prenom', 'date_naissance',
                'code_postal', 'ville', 'numero_securite_sociale', 'numero_ins'
            ]
            
            for field in required_fields:
                if field not in demo or not demo[field]:
                    return None
            
            # Validation et correction du code postal
            code_postal = str(demo['code_postal']).zfill(5)
            if code_postal not in self.codes_postaux_villes:
                # Utiliser un code postal valide aléatoire
                code_postal = random.choice(list(self.codes_postaux_villes.keys()))
            
            demo['code_postal'] = code_postal
            demo['ville'] = self.codes_postaux_villes[code_postal]
            
            # Validation de la date de naissance
            date_str = demo['date_naissance']
            try:
                date_obj = datetime.strptime(date_str, '%d/%m/%Y')
                if date_obj.year < 1920 or date_obj.year > 2023:
                    # Générer une date valide
                    year = random.randint(1920, 2023)
                    month = random.randint(1, 12)
                    day = random.randint(1, 28)
                    demo['date_naissance'] = f"{day:02d}/{month:02d}/{year}"
            except:
                # Date invalide, en générer une nouvelle
                year = random.randint(1920, 2023)
                month = random.randint(1, 12)
                day = random.randint(1, 28)
                demo['date_naissance'] = f"{day:02d}/{month:02d}/{year}"
            
            # Validation du numéro de sécurité sociale
            if not self.is_valid_secu_format(demo['numero_securite_sociale']):
                demo['numero_securite_sociale'] = self.generate_secu_number(
                    demo['date_naissance'], code_postal
                )
            
            # Validation du numéro INS
            if not demo['numero_ins'].isdigit() or len(demo['numero_ins']) != 8:
                demo['numero_ins'] = f"{random.randint(10000000, 99999999)}"
            
            # Nettoyer les champs texte
            demo['nom_naissance'] = demo['nom_naissance'].upper().strip()
            demo['nom_usage'] = demo['nom_usage'].upper().strip()
            demo['prenom'] = demo['prenom'].title().strip()
            demo['ville'] = demo['ville'].title().strip()
            
            return demo
            
        except Exception as e:
            print(f"Erreur validation: {e}")
            return None
    
    def is_valid_secu_format(self, secu: str) -> bool:
        """Vérifie le format du numéro de sécurité sociale"""
        # Format attendu: "1 85 03 69 123 456 78" (15 chiffres + espaces)
        cleaned = re.sub(r'\s+', '', secu)
        return len(cleaned) == 15 and cleaned.isdigit()
    
    def generate_secu_number(self, date_naissance: str, code_postal: str) -> str:
        """Génère un numéro de sécurité sociale valide"""
        try:
            date_obj = datetime.strptime(date_naissance, '%d/%m/%Y')
            
            # Déterminer le sexe aléatoirement
            sexe_code = random.choice(['1', '2'])
            annee = str(date_obj.year)[2:]
            mois = f"{date_obj.month:02d}"
            dept = code_postal[:2]
            commune = f"{random.randint(1, 999):03d}"
            ordre = f"{random.randint(1, 999):03d}"
            cle = f"{random.randint(10, 99)}"
            
            return f"{sexe_code} {annee} {mois} {dept} {commune} {ordre} {cle}"
        except:
            return "1 85 01 75 123 456 78"  # Fallback
    
    def generate_batch(self, batch_size: Optional[int] = None) -> List[Dict]:
        """Génère un lot de démographies"""
        if batch_size is None:
            batch_size = self.batch_size
        
        prompt = self.create_prompt(batch_size)
        response = self.ollama.generate(prompt, temperature=0.8)
        
        if response:
            demographics = self.parse_llm_response(response)
            return demographics
        
        return []
    
    def generate_demographics(self, total_count: int, output_file: str):
        """Génère le fichier complet de démographies"""
        print(f"🤖 Génération de {total_count} démographies avec {self.ollama.model}")
        print(f"📄 Fichier de sortie: {output_file}")
        
        headers = [
            'nom_naissance', 'nom_usage', 'prenom', 'date_naissance',
            'code_postal', 'ville', 'numero_securite_sociale', 'numero_ins'
        ]
        
        generated_count = 0
        batch_number = 1
        
        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=headers, delimiter=';')
            writer.writeheader()
            
            while generated_count < total_count:
                remaining = total_count - generated_count
                current_batch_size = min(self.batch_size, remaining)
                
                print(f"🔄 Lot {batch_number}: génération de {current_batch_size} démographies...")
                
                # Retry logic pour les échecs LLM
                demographics = []
                max_retries = 3
                for attempt in range(max_retries):
                    demographics = self.generate_batch(current_batch_size)
                    if demographics:
                        break
                    print(f"   ⚠️  Tentative {attempt + 1}/{max_retries} échouée, retry...")
                    time.sleep(2)
                
                if not demographics:
                    print(f"   ❌ Échec du lot {batch_number}, utilisation du générateur de fallback")
                    demographics = self.generate_fallback_batch(current_batch_size)
                
                # Écrire les démographies valides
                for demo in demographics[:current_batch_size]:
                    writer.writerow(demo)
                    generated_count += 1
                    
                    if generated_count % 100 == 0:
                        progress = (generated_count / total_count) * 100
                        print(f"   📊 Progression: {generated_count}/{total_count} ({progress:.1f}%)")
                
                batch_number += 1
                
                # Pause pour éviter la surcharge d'Ollama
                if generated_count < total_count:
                    time.sleep(1)
        
        print(f"✅ Génération terminée: {generated_count} démographies dans {output_file}")
    
    def generate_fallback_batch(self, batch_size: int) -> List[Dict]:
        """Générateur de fallback si le LLM échoue"""
        from faker import Faker
        fake = Faker('fr_FR')
        
        demographics = []
        for _ in range(batch_size):
            code_postal = random.choice(list(self.codes_postaux_villes.keys()))
            ville = self.codes_postaux_villes[code_postal]
            
            date_naissance = fake.date_of_birth(minimum_age=1, maximum_age=100)
            
            demo = {
                'nom_naissance': fake.last_name().upper(),
                'nom_usage': fake.last_name().upper(),
                'prenom': fake.first_name().title(),
                'date_naissance': date_naissance.strftime('%d/%m/%Y'),
                'code_postal': code_postal,
                'ville': ville,
                'numero_securite_sociale': self.generate_secu_number(
                    date_naissance.strftime('%d/%m/%Y'), code_postal
                ),
                'numero_ins': f"{random.randint(10000000, 99999999)}"
            }
            demographics.append(demo)
        
        return demographics

def check_ollama_setup(timeout: int = 120):
    """Vérifie et configure Ollama"""
    client = OllamaClient(timeout=timeout)
    
    if not client.is_available():
        print("❌ Ollama n'est pas accessible sur http://localhost:11434")
        print("📋 Pour installer et démarrer Ollama:")
        print("   1. Téléchargez Ollama: https://ollama.ai/")
        print("   2. Démarrez le service: ollama serve")
        print("   3. Installez un modèle: ollama pull llama3.2")
        return None
    
    models = client.list_models()
    if not models:
        print("❌ Aucun modèle trouvé dans Ollama")
        print("📋 Installez un modèle recommandé:")
        print("   ollama pull llama3.2        # Équilibré (4.7GB)")
        print("   ollama pull gemma2:2b        # Léger (1.6GB)")
        print("   ollama pull qwen2.5:3b       # Performant (1.9GB)")
        return None
    
    print(f"✅ Ollama détecté avec {len(models)} modèle(s): {', '.join(models)}")
    
    # Sélectionner le meilleur modèle disponible
    preferred_models = ['llama3.2', 'gemma2:2b', 'qwen2.5:3b', 'llama3.1', 'gemma2']
    selected_model = None
    
    for preferred in preferred_models:
        for available in models:
            if available.startswith(preferred):
                selected_model = available
                break
        if selected_model:
            break
    
    if not selected_model:
        selected_model = models[0]
    
    print(f"🎯 Modèle sélectionné: {selected_model}")
    print(f"⏱️  Timeout configuré: {client.timeout}s")
    client.model = selected_model

    return client

def main():
    parser = argparse.ArgumentParser(
        description="Générateur de démographies avec SLM (Ollama)"
    )
    parser.add_argument(
        '-n', '--nombre',
        type=int,
        default=200000,
        help="Nombre de démographies à générer (défaut: 200000)"
    )
    parser.add_argument(
        '-o', '--output',
        type=str,
        default='demographics_llm.csv',
        help="Nom du fichier de sortie (défaut: demographics_llm.csv)"
    )
    parser.add_argument(
        '-m', '--model',
        type=str,
        help="Modèle Ollama à utiliser (auto-détection si non spécifié)"
    )
    parser.add_argument(
        '--batch-size',
        type=int,
        default=10,
        help="Taille des lots pour les requêtes LLM (défaut: 10)"
    )
    parser.add_argument(
        '--test',
        action='store_true',
        help="Mode test: génère seulement 50 démographies"
    )
    parser.add_argument(
        '--timeout',
        type=int,
        default=120,
        help="Timeout en secondes pour les requêtes Ollama (défaut: 120)"
    )
    
    args = parser.parse_args()
    
    # Mode test
    if args.test:
        args.nombre = 50
        args.output = 'demographics_test.csv'
        print("🧪 Mode test activé (50 démographies)")
    
    try:
        # Vérifier Ollama
        client = check_ollama_setup(timeout=args.timeout)
        if not client:
            return 1
        
        # Modèle personnalisé
        if args.model:
            available_models = client.list_models()
            if args.model in available_models:
                client.model = args.model
                print(f"🎯 Utilisation du modèle: {args.model}")
            else:
                print(f"❌ Modèle {args.model} non trouvé")
                print(f"Modèles disponibles: {', '.join(available_models)}")
                return 1
        
        # Créer le générateur
        generator = DemographicsGenerator(client)
        generator.batch_size = args.batch_size
        
        # Test de connexion
        print("🔧 Test du modèle...")
        test_demographics = generator.generate_batch(2)
        if not test_demographics:
            print("❌ Échec du test du modèle")
            return 1
        
        print(f"✅ Test réussi - exemple généré:")
        for demo in test_demographics[:1]:
            print(f"   {demo['prenom']} {demo['nom_usage']} - {demo['ville']}")
        
        # Génération complète
        start_time = time.time()
        generator.generate_demographics(args.nombre, args.output)
        
        duration = time.time() - start_time
        print(f"⏱️  Temps total: {duration:.1f}s ({args.nombre/duration:.1f} démographies/s)")
        
        return 0
        
    except KeyboardInterrupt:
        print("\n❌ Génération interrompue par l'utilisateur")
        return 1
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())