#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Générateur de démographies fictives pour tests
Crée un fichier CSV avec 200 000 démographies françaises fictives
"""

import csv
import random
from datetime import datetime, timedelta
from faker import Faker
import argparse
import sys

# Configuration Faker pour la France
fake = Faker('fr_FR')

# Listes de noms et prénoms français typiques
NOMS_FRANCAIS = [
    # Noms français traditionnels (en majuscules)
    'MARTIN', 'BERNARD', 'THOMAS', 'PETIT', 'ROBERT', 'RICHARD', 'DURAND', 'DUBOIS',
    'MOREAU', 'LAURENT', 'SIMON', 'MICHEL', 'LEFEBVRE', 'LEROY', 'ROUX', 'DAVID',
    'BERTRAND', 'MOR<PERSON>', 'FOURNIER', 'GIRARD', 'BONNET', 'DUPONT', 'LAMBERT', 'FONTAINE',
    'ROUSSEAU', 'VINCENT', 'MULLER', 'LEFEVRE', 'FAURE', 'ANDRE', 'MERCIER', 'BLANC',
    'GUERIN', 'BOYER', 'GARNIER', 'CHEVALIER', 'FRANCOIS', 'LEGRAND', 'GAUTHIER', 'GARCIA',
    'PERRIN', 'ROBIN', 'CLEMENT', 'MORIN', 'NICOLAS', 'HENRY', 'ROUSSEL', 'MATHIEU',
    'GAUTIER', 'MASSON', 'MARCHAND', 'DUVAL', 'DENIS', 'DUMONT', 'MARIE', 'LEMAIRE',
    'NOEL', 'MEYER', 'DUFOUR', 'MEUNIER', 'BRUN', 'BLANCHARD', 'GIRAUD', 'JOLY',
    'RIVIERE', 'LUCAS', 'BRUNET', 'GAILLARD', 'BARBIER', 'ARNAUD', 'MARTINEZ', 'GERARD',
    'ROCHE', 'RENARD', 'SCHMITT', 'ROY', 'LEROUX', 'COLIN', 'VIDAL', 'CARON',
    'PICARD', 'ROGER', 'FABRE', 'AUBERT', 'LEMOINE', 'RENAUD', 'DUMAS', 'LACROIX',
    'OLIVIER', 'PHILIPPE', 'BOURGEOIS', 'PIERRE', 'BENOIT', 'REY', 'LECLERC', 'PAYET',
    'ROLLAND', 'LECLERCQ', 'GUILLAUME', 'LECOMTE', 'LOPEZ', 'JEAN', 'DUPUY', 'GUILLOT',
    'HUBERT', 'BERGER', 'CARPENTIER', 'SANCHEZ', 'DUPUIS', 'MOULIN', 'LOUIS', 'DESCHAMPS',
    
    # Noms d'origine asiatique (francisés ou présents en France)
    'NGUYEN', 'CHEN', 'WANG', 'LI', 'ZHANG', 'HUANG', 'WU', 'ZHAO', 'ZHOU', 'XU',
    'SUN', 'MA', 'ZHU', 'HU', 'GUO', 'HE', 'GAO', 'LIN', 'LUO', 'ZHENG',
    'LIANG', 'XIE', 'SONG', 'TANG', 'XU', 'HAN', 'DENG', 'FENG', 'CAO', 'PENG',
    'TRAN', 'LE', 'PHAM', 'HOANG', 'VU', 'VO', 'TRUONG', 'BUI', 'DO', 'HO',
    'NGO', 'DUONG', 'LY', 'QUACH', 'TA', 'PHAN', 'HUA', 'MAI', 'KHOA', 'DINH',
    'TAKAHASHI', 'TANAKA', 'WATANABE', 'ITO', 'YAMAMOTO', 'NAKAMURA', 'KOBAYASHI', 'KATO',
    'YOSHIDA', 'YAMADA', 'SASAKI', 'YAMAGUCHI', 'MATSUMOTO', 'INOUE', 'KIMURA', 'HAYASHI',
    'SHIMIZU', 'YAMAZAKI', 'MORI', 'ABE', 'IKEDA', 'HASHIMOTO', 'YAMASHITA', 'ISHIKAWA',
    
    # Noms d'origine africaine (francisés ou présents en France)
    'TRAORE', 'DIALLO', 'DIOP', 'DIOUF', 'FALL', 'NDIAYE', 'SYLLA', 'CAMARA',
    'BARRY', 'KONE', 'SOW', 'KANE', 'CISSE', 'TOURE', 'DIARRA', 'KEITA',
    'SANGARE', 'COULIBALY', 'SIDIBE', 'FOFANA', 'OUEDRAOGO', 'KABORE', 'SAWADOGO', 'COMPAORE',
    'ZONGO', 'OUATTARA', 'KONATE', 'BAGAYOKO', 'MAIGA', 'DOUMBIA', 'SAMAKE', 'DEMBELE',
    'BA', 'GUEYE', 'SARR', 'WADE', 'SECK', 'FAYE', 'THIAM', 'MBAYE',
    'DIENG', 'NDOYE', 'DIEME', 'LO', 'TALL', 'WANE', 'SENE', 'GAYE',
    'DIABATE', 'KANTE', 'BAMBA', 'YAPI', 'KOFFI', 'KOUAME', 'ASSI', 'KOUASSI',
    'N\'GUESSAN', 'KONE', 'ADOU', 'GNAGNE', 'BROU', 'TANOH', 'DJIAN', 'KONAN',
    
   
    # Noms composés avec tiret (style particule)
    'DE-LA-FONTAINE', 'DE-MONTCLAIR', 'DU-BARRY', 'DE-VILLENEUVE', 'DE-SAINT-MARTIN', 'DU-PONT',
    'DE-BEAUMONT', 'DE-ROCHELLE', 'DE-MONTPELLIER', 'DU-MOULIN', 'DE-SAINT-PIERRE', 'DE-VALOIS',
    'DE-BRETAGNE', 'DU-TEMPLE', 'DE-FLEUR', 'DE-MONTAGNE', 'DU-BOIS-MARIE', 'DE-SAINT-JEAN'
]

PRENOMS_MASCULINS = [
    'Jean', 'Pierre', 'Michel', 'André', 'Philippe', 'Alain', 'Bernard', 'Claude',
    'Daniel', 'Henri', 'Jacques', 'Robert', 'René', 'Louis', 'Paul', 'François',
    'Marcel', 'Antoine', 'Christian', 'Nicolas', 'Gérard', 'Patrick', 'Olivier',
    'Stéphane', 'Laurent', 'Julien', 'Emmanuel', 'David', 'Thierry', 'Frédéric',
    'Sébastien', 'Christophe', 'Vincent', 'Alexandre', 'Maxime', 'Thomas', 'Lucas',
    'Nathan', 'Hugo', 'Arthur', 'Louis', 'Gabriel', 'Raphaël', 'Adam', 'Victor',
    'Jules', 'Maël', 'Paul', 'Léo', 'Noah', 'Ethan', 'Sacha', 'Aaron', 'Tom'
]

PRENOMS_FEMININS = [
    'Marie', 'Françoise', 'Monique', 'Catherine', 'Brigitte', 'Nicole', 'Annie',
    'Sylvie', 'Christine', 'Isabelle', 'Martine', 'Nathalie', 'Valérie', 'Sandrine',
    'Véronique', 'Christelle', 'Céline', 'Patricia', 'Stéphanie', 'Caroline',
    'Virginie', 'Sophie', 'Aurélie', 'Emilie', 'Julie', 'Manon', 'Chloé', 'Sarah',
    'Laura', 'Marine', 'Camille', 'Emma', 'Jade', 'Léa', 'Pauline', 'Inès',
    'Louise', 'Alice', 'Lola', 'Mila', 'Ambre', 'Zoé', 'Clara', 'Rose', 'Anna',
    'Juliette', 'Mathilde', 'Romane', 'Manon', 'Océane', 'Maëlys', 'Eva'
]

# Prénoms composés par sexe pour une meilleure gestion
PRENOMS_COMPOSES_MASCULINS = [
    'Jean-Pierre', 'Jean-Claude', 'Jean-Paul', 'Jean-Luc', 'Jean-Michel', 'Jean-François',
    'Jean-Jacques', 'Jean-Baptiste', 'Jean-Marie', 'Jean-Charles', 'Jean-Philippe', 'Jean-Christophe',
    'Jean-Sébastien', 'Jean-Robert', 'Jean-Daniel', 'Pierre-Henri', 'Pierre-Yves', 'Pierre-Louis',
    'Pierre-Marie', 'Pierre-Alain', 'Pierre-Jean', 'Louis-Marie', 'Charles-Henri', 'François-Xavier',
    'Antoine-Marie', 'Michel-Henri', 'Paul-Henri', 'André-Marie', 'Claude-Marie', 'Daniel-Marie',
    'Patrick-Marie', 'Bernard-Marie', 'Robert-Marie', 'Marc-Antoine', 'Paul-Émile', 'Henri-Louis',
    'Jean-Noël', 'Jean-René', 'Jean-Yves', 'Pierre-Paul', 'Louis-Philippe', 'Charles-Édouard'
]

PRENOMS_COMPOSES_FEMININS = [
    'Marie-Claire', 'Anne-Marie', 'Marie-France', 'Marie-Thérèse', 'Marie-Christine', 'Marie-Jeanne',
    'Marie-Hélène', 'Marie-Antoinette', 'Marie-Louise', 'Marie-Claude', 'Marie-Noëlle', 'Marie-Agnès',
    'Marie-Catherine', 'Marie-Dominique', 'Marie-Françoise', 'Anne-Sophie', 'Marie-Pierre', 'Marie-Laure',
    'Marie-Paule', 'Marie-José', 'Marie-Odile', 'Marie-Ange', 'Marie-Rose', 'Marie-Madeleine',
    'Anne-Catherine', 'Anne-Laure', 'Anne-Françoise', 'Claire-Marie', 'Élise-Marie', 'Sophie-Marie',
    'Jeanne-Marie', 'Brigitte-Marie', 'Sylvie-Marie', 'Catherine-Marie', 'Isabelle-Marie', 'Véronique-Marie'
]

# Civilités par sexe
CIVILITES_MASCULINES = ['M.', 'Mr', 'Monsieur']
CIVILITES_FEMININES = ['Mme', 'Madame', 'Mlle', 'Mademoiselle']

# Codes postaux français par région (échantillon représentatif)
CODES_POSTAUX_VILLES = {
    # Île-de-France
    '75001': 'Paris', '75002': 'Paris', '75003': 'Paris', '75004': 'Paris', '75005': 'Paris',
    '75006': 'Paris', '75007': 'Paris', '75008': 'Paris', '75009': 'Paris', '75010': 'Paris',
    '75011': 'Paris', '75012': 'Paris', '75013': 'Paris', '75014': 'Paris', '75015': 'Paris',
    '75016': 'Paris', '75017': 'Paris', '75018': 'Paris', '75019': 'Paris', '75020': 'Paris',
    '92100': 'Boulogne-Billancourt', '92200': 'Neuilly-sur-Seine', '92300': 'Levallois-Perret',
    '93200': 'Saint-Denis', '94000': 'Créteil', '95000': 'Cergy',
    
    # Provence-Alpes-Côte d\'Azur
    '13001': 'Marseille', '13002': 'Marseille', '13003': 'Marseille', '13004': 'Marseille',
    '13005': 'Marseille', '13006': 'Marseille', '13007': 'Marseille', '13008': 'Marseille',
    '06000': 'Nice', '06100': 'Nice', '06200': 'Nice', '83000': 'Toulon', '84000': 'Avignon',
    
    # Auvergne-Rhône-Alpes
    '69001': 'Lyon', '69002': 'Lyon', '69003': 'Lyon', '69004': 'Lyon', '69005': 'Lyon',
    '69006': 'Lyon', '69007': 'Lyon', '69008': 'Lyon', '69009': 'Lyon',
    '38000': 'Grenoble', '73000': 'Chambéry', '74000': 'Annecy', '42000': 'Saint-Étienne',
    
    # Nouvelle-Aquitaine
    '33000': 'Bordeaux', '33100': 'Bordeaux', '33200': 'Bordeaux', '33300': 'Bordeaux',
    '64000': 'Pau', '87000': 'Limoges', '86000': 'Poitiers', '17000': 'La Rochelle',
    
    # Occitanie
    '31000': 'Toulouse', '31100': 'Toulouse', '31200': 'Toulouse', '31300': 'Toulouse',
    '34000': 'Montpellier', '30000': 'Nîmes', '66000': 'Perpignan', '11000': 'Carcassonne',
    
    # Grand Est
    '67000': 'Strasbourg', '68000': 'Colmar', '54000': 'Nancy', '57000': 'Metz',
    '51100': 'Reims', '10000': 'Troyes', '88000': 'Épinal',
    
    # Hauts-de-France
    '59000': 'Lille', '59100': 'Roubaix', '59200': 'Tourcoing', '62000': 'Arras',
    '80000': 'Amiens', '02000': 'Laon', '60000': 'Beauvais',
    
    # Normandie
    '76000': 'Rouen', '76100': 'Rouen', '76600': 'Le Havre', '14000': 'Caen',
    '50000': 'Saint-Lô', '27000': 'Évreux', '61000': 'Alençon',
    
    # Bretagne
    '35000': 'Rennes', '29000': 'Quimper', '56000': 'Vannes', '22000': 'Saint-Brieuc',
    
    # Centre-Val de Loire
    '45000': 'Orléans', '37000': 'Tours', '41000': 'Blois', '28000': 'Chartres',
    
    # Pays de la Loire
    '44000': 'Nantes', '49000': 'Angers', '72000': 'Le Mans', '85000': 'La Roche-sur-Yon',
    
    # Bourgogne-Franche-Comté
    '21000': 'Dijon', '71000': 'Mâcon', '89000': 'Auxerre', '25000': 'Besançon'
}

def generer_numero_securite_sociale(sexe, date_naissance, code_postal):
    """
    Génère un numéro de sécurité sociale français fictif mais cohérent
    Format: 1AABBCCDDDEEE KK
    """
    # Sexe (1 = homme, 2 = femme)
    sexe_code = '1' if sexe == 'M' else '2'
    
    # Année de naissance (2 derniers chiffres)
    annee = str(date_naissance.year)[2:]
    
    # Mois de naissance
    mois = f"{date_naissance.month:02d}"
    
    # Département de naissance (2 premiers chiffres du code postal)
    dept = code_postal[:2]
    
    # Commune (3 chiffres aléatoires)
    commune = f"{random.randint(1, 999):03d}"
    
    # Numéro d'ordre (3 chiffres aléatoires)
    ordre = f"{random.randint(1, 999):03d}"
    
    # Numéro sans clé
    numero_sans_cle = sexe_code + annee + mois + dept + commune + ordre
    
    # Calcul de la clé (simplifié - pas le vrai algorithme)
    cle = f"{random.randint(10, 99)}"
    
    return numero_sans_cle + ' ' + cle

def generer_numero_ins():
    """
    Génère un numéro INS (Identifiant National de Santé) fictif
    Format: Numéro sécurité sociale + OID (matricule)
    """
    # OID fictif (8 chiffres)
    oid = f"{random.randint(10000000, 99999999)}"
    return oid

def generer_date_naissance():
    """
    Génère une date de naissance réaliste entre 1920 et 2023
    """
    start_date = datetime(1920, 1, 1)
    end_date = datetime(2023, 12, 31)
    
    # Distribution pondérée par âge (plus de personnes jeunes/moyennes)
    weights = []
    current_year = 2024
    
    for year in range(1920, 2024):
        age = current_year - year
        if age < 20:
            weight = 1.5  # Jeunes
        elif age < 65:
            weight = 3.0  # Adultes actifs
        elif age < 85:
            weight = 2.0  # Retraités
        else:
            weight = 0.5  # Très âgés
        weights.append(weight)
    
    # Sélection pondérée d'une année
    year = random.choices(range(1920, 2024), weights=weights)[0]
    
    # Date aléatoire dans l'année
    start_of_year = datetime(year, 1, 1)
    end_of_year = datetime(year, 12, 31)
    random_date = start_of_year + timedelta(
        days=random.randint(0, (end_of_year - start_of_year).days)
    )
    
    return random_date

def generer_civilite(sexe, age):
    """
    Génère une civilité appropriée selon le sexe et l'âge
    """
    if sexe == 'M':
        # Pour les hommes, principalement M. ou Monsieur
        return random.choices(
            CIVILITES_MASCULINES,
            weights=[70, 20, 10],  # M. plus fréquent
            k=1
        )[0]
    else:
        # Pour les femmes, Mlle pour les jeunes, Mme pour les plus âgées
        if age < 25:
            return random.choices(
                ['Mlle', 'Mademoiselle', 'Mme'],
                weights=[60, 20, 20],
                k=1
            )[0]
        else:
            return random.choices(
                ['Mme', 'Madame', 'Mlle'],
                weights=[70, 20, 10],
                k=1
            )[0]

def generer_nom_usage(nom_naissance, sexe):
    """
    Génère un nom d'usage (parfois différent du nom de naissance pour les femmes mariées)
    """
    # 70% des femmes gardent leur nom de naissance ou prennent celui du mari
    # 30% ont un nom d'usage différent (mariage)
    if sexe == 'F' and random.random() < 0.3:
        return random.choice(NOMS_FRANCAIS)
    else:
        return nom_naissance

def generer_prenom(sexe):
    """
    Génère un prénom en incluant la possibilité de prénoms composés
    """
    # 20% de chance d'avoir un prénom composé (plus fréquent qu'avant)
    if random.random() < 0.2:
        if sexe == 'M':
            return random.choice(PRENOMS_COMPOSES_MASCULINS)
        else:
            return random.choice(PRENOMS_COMPOSES_FEMININS)

    # Sinon, prénom simple
    if sexe == 'M':
        prenom = random.choice(PRENOMS_MASCULINS)
    else:
        prenom = random.choice(PRENOMS_FEMININS)

    # 25% de chance d'avoir un deuxième prénom
    if random.random() < 0.25:
        if sexe == 'M':
            prenom += ' ' + random.choice(PRENOMS_MASCULINS)
        else:
            prenom += ' ' + random.choice(PRENOMS_FEMININS)

    return prenom

def generer_demographie():
    """
    Génère une démographie complète avec sexe et civilité
    """
    # Sexe
    sexe = random.choice(['M', 'F'])

    # Date de naissance (nécessaire pour calculer l'âge pour la civilité)
    date_naissance = generer_date_naissance()
    age = 2024 - date_naissance.year

    # Civilité basée sur le sexe et l'âge
    civilite = generer_civilite(sexe, age)

    # Noms et prénoms
    nom_naissance = random.choice(NOMS_FRANCAIS)
    nom_usage = generer_nom_usage(nom_naissance, sexe)

    # Génération du prénom (avec possibilité de prénoms composés)
    prenom = generer_prenom(sexe)

    # Localisation
    code_postal = random.choice(list(CODES_POSTAUX_VILLES.keys()))
    ville = CODES_POSTAUX_VILLES[code_postal]

    # Numéros
    num_secu = generer_numero_securite_sociale(sexe, date_naissance, code_postal)
    num_ins = generer_numero_ins()

    return {
        'civilite': civilite,
        'sexe': sexe,
        'nom_naissance': nom_naissance.upper(),
        'nom_usage': nom_usage.upper(),
        'prenom': prenom.title(),
        'date_naissance': date_naissance.strftime('%d/%m/%Y'),
        'code_postal': code_postal,
        'ville': ville,
        'numero_securite_sociale': num_secu,
        'numero_ins': num_ins
    }

def creer_fichier_demographics(nombre_lignes=200000, nom_fichier='demographics.csv'):
    """
    Crée le fichier CSV avec les démographies
    """
    print(f"Génération de {nombre_lignes} démographies fictives...")
    print(f"Fichier de sortie: {nom_fichier}")
    
    headers = [
        'civilite',
        'sexe',
        'nom_naissance',
        'nom_usage',
        'prenom',
        'date_naissance',
        'code_postal',
        'ville',
        'numero_securite_sociale',
        'numero_ins'
    ]
    
    with open(nom_fichier, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=headers, delimiter=';')
        
        # Écriture de l'en-tête
        writer.writeheader()
        
        # Génération et écriture des données
        for i in range(nombre_lignes):
            if i % 10000 == 0:
                print(f"Progression: {i}/{nombre_lignes} ({i/nombre_lignes*100:.1f}%)")
            
            demographie = generer_demographie()
            writer.writerow(demographie)
    
    print(f"SUCCES: Fichier {nom_fichier} créé avec succès!")
    print(f"STATS: {nombre_lignes} démographies générées")

def afficher_statistiques(nom_fichier):
    """
    Affiche quelques statistiques sur le fichier généré
    """
    print(f"\n📈 Statistiques du fichier {nom_fichier}:")

    try:
        with open(nom_fichier, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile, delimiter=';')
            demographics = list(reader)

        total = len(demographics)
        print(f"Total des enregistrements: {total}")

        # Répartition par sexe
        sexes = {}
        civilites = {}
        prenoms_composes = 0

        for demo in demographics:
            sexe = demo.get('sexe', 'N/A')
            sexes[sexe] = sexes.get(sexe, 0) + 1

            civilite = demo.get('civilite', 'N/A')
            civilites[civilite] = civilites.get(civilite, 0) + 1

            # Compter les prénoms composés (contiennent un tiret)
            if '-' in demo.get('prenom', ''):
                prenoms_composes += 1

        print(f"\nRépartition par sexe:")
        for sexe, count in sexes.items():
            print(f"  {sexe}: {count} ({count/total*100:.1f}%)")

        print(f"\nRépartition par civilité:")
        for civilite, count in sorted(civilites.items(), key=lambda x: x[1], reverse=True):
            print(f"  {civilite}: {count} ({count/total*100:.1f}%)")

        print(f"\nPrénoms composés: {prenoms_composes} ({prenoms_composes/total*100:.1f}%)")

        # Répartition par ville (top 10)
        villes = {}
        for demo in demographics:
            ville = demo['ville']
            villes[ville] = villes.get(ville, 0) + 1

        print(f"\nTop 10 des villes:")
        for ville, count in sorted(villes.items(), key=lambda x: x[1], reverse=True)[:10]:
            print(f"  {ville}: {count} ({count/total*100:.1f}%)")

        # Exemple de quelques lignes
        print(f"\nExemples d'enregistrements:")
        for i, demo in enumerate(demographics[:5]):
            civilite = demo.get('civilite', '')
            sexe = demo.get('sexe', '')
            prenom = demo.get('prenom', '')
            nom = demo.get('nom_usage', '')
            date = demo.get('date_naissance', '')
            ville = demo.get('ville', '')
            print(f"  {i+1}. {civilite} {prenom} {nom} ({sexe}) - {date} - {ville}")

    except Exception as e:
        print(f"Erreur lors de l'analyse: {e}")

def main():
    parser = argparse.ArgumentParser(
        description="Générateur de démographies fictives pour tests HL7"
    )
    parser.add_argument(
        '--export',
        type=int,
        help="Nombre de démographies à exporter (recommandé)"
    )
    parser.add_argument(
        '-o', '--output',
        type=str,
        default='demographics.csv',
        help="Nom du fichier de sortie (défaut: demographics.csv)"
    )
    parser.add_argument(
        '--stats',
        action='store_true',
        help="Afficher les statistiques après génération"
    )
    # Garder -n pour compatibilité mais le rendre optionnel
    parser.add_argument(
        '-n', '--nombre',
        type=int,
        help="Nombre de démographies à générer (déprécié, utilisez --export)"
    )
    
    args = parser.parse_args()

    # Déterminer le nombre de lignes à générer
    if args.export:
        nombre_lignes = args.export
    elif args.nombre:
        nombre_lignes = args.nombre
        print("ATTENTION: L'option -n/--nombre est dépréciée, utilisez --export à la place")
    else:
        print("ERREUR: Vous devez spécifier le nombre de lignes avec --export")
        print("   Exemple: python demographics_generator_parse.py --export 500")
        sys.exit(1)

    try:
        creer_fichier_demographics(nombre_lignes, args.output)

        if args.stats:
            afficher_statistiques(args.output)

    except KeyboardInterrupt:
        print("\nGénération interrompue par l'utilisateur")
        sys.exit(1)
    except Exception as e:
        print(f"ERREUR: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()