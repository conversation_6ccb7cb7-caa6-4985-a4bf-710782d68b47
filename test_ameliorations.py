#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de test pour valider les améliorations du générateur de démographies
"""

import csv
import sys
import os

# Ajouter le répertoire courant au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from demographics_generator_parse import (
    generer_demographie, 
    PRENOMS_COMPOSES_MASCULINS, 
    PRENOMS_COMPOSES_FEMININS,
    CIVILITES_MASCULINES,
    CIVILITES_FEMININES
)

def test_generation_complete():
    """Test de génération d'une démographie complète"""
    print("🧪 Test de génération complète")
    print("=" * 40)
    
    demo = generer_demographie()
    
    # Vérifier que tous les champs sont présents
    champs_requis = [
        'civilite', 'sexe', 'nom_naissance', 'nom_usage', 
        'prenom', 'date_naissance', 'code_postal', 'ville',
        'numero_securite_sociale', 'numero_ins'
    ]
    
    for champ in champs_requis:
        if champ not in demo:
            print(f"❌ Champ manquant: {champ}")
            return False
        if not demo[champ]:
            print(f"❌ Champ vide: {champ}")
            return False
    
    print("✅ Tous les champs sont présents et remplis")
    
    # Afficher l'exemple
    print(f"\n📋 Exemple généré:")
    print(f"   Civilité: {demo['civilite']}")
    print(f"   Sexe: {demo['sexe']}")
    print(f"   Nom: {demo['nom_naissance']}")
    print(f"   Nom d'usage: {demo['nom_usage']}")
    print(f"   Prénom: {demo['prenom']}")
    print(f"   Date naissance: {demo['date_naissance']}")
    print(f"   Ville: {demo['ville']} ({demo['code_postal']})")
    print(f"   Sécurité sociale: {demo['numero_securite_sociale']}")
    
    return True

def test_coherence_sexe_civilite():
    """Test de cohérence entre sexe et civilité"""
    print("\n🧪 Test de cohérence sexe/civilité")
    print("=" * 40)
    
    erreurs = 0
    echantillon = 50
    
    for i in range(echantillon):
        demo = generer_demographie()
        sexe = demo['sexe']
        civilite = demo['civilite']
        
        if sexe == 'M' and civilite not in CIVILITES_MASCULINES:
            print(f"❌ Incohérence: Homme avec civilité féminine ({civilite})")
            erreurs += 1
        elif sexe == 'F' and civilite not in CIVILITES_FEMININES:
            print(f"❌ Incohérence: Femme avec civilité masculine ({civilite})")
            erreurs += 1
    
    if erreurs == 0:
        print(f"✅ Cohérence parfaite sur {echantillon} échantillons")
    else:
        print(f"❌ {erreurs} incohérences détectées sur {echantillon}")
    
    return erreurs == 0

def test_prenoms_composes():
    """Test de la fréquence des prénoms composés"""
    print("\n🧪 Test des prénoms composés")
    print("=" * 40)
    
    echantillon = 200
    composes_masculins = 0
    composes_feminins = 0
    total_masculins = 0
    total_feminins = 0
    
    for i in range(echantillon):
        demo = generer_demographie()
        prenom = demo['prenom']
        sexe = demo['sexe']
        
        if sexe == 'M':
            total_masculins += 1
            if prenom in PRENOMS_COMPOSES_MASCULINS:
                composes_masculins += 1
        else:
            total_feminins += 1
            if prenom in PRENOMS_COMPOSES_FEMININS:
                composes_feminins += 1
    
    total_composes = composes_masculins + composes_feminins
    pourcentage_total = (total_composes / echantillon) * 100
    
    print(f"📊 Résultats sur {echantillon} échantillons:")
    print(f"   Prénoms composés masculins: {composes_masculins}/{total_masculins}")
    print(f"   Prénoms composés féminins: {composes_feminins}/{total_feminins}")
    print(f"   Total prénoms composés: {total_composes} ({pourcentage_total:.1f}%)")
    
    # On s'attend à environ 15-25% de prénoms composés
    if 10 <= pourcentage_total <= 30:
        print("✅ Fréquence des prénoms composés dans la plage attendue")
        return True
    else:
        print(f"⚠️  Fréquence des prénoms composés hors plage (attendu: 10-30%)")
        return False

def test_diversite_civilites():
    """Test de la diversité des civilités"""
    print("\n🧪 Test de diversité des civilités")
    print("=" * 40)
    
    echantillon = 100
    civilites_trouvees = set()
    
    for i in range(echantillon):
        demo = generer_demographie()
        civilites_trouvees.add(demo['civilite'])
    
    print(f"📊 Civilités trouvées: {sorted(civilites_trouvees)}")
    print(f"   Nombre de civilités différentes: {len(civilites_trouvees)}")
    
    # On s'attend à au moins 4 civilités différentes
    if len(civilites_trouvees) >= 4:
        print("✅ Bonne diversité des civilités")
        return True
    else:
        print("⚠️  Diversité des civilités insuffisante")
        return False

def test_generation_fichier():
    """Test de génération d'un fichier complet"""
    print("\n🧪 Test de génération de fichier")
    print("=" * 40)
    
    from demographics_generator_parse import creer_fichier_demographics
    
    nom_fichier = "test_validation.csv"
    nombre_lignes = 50
    
    try:
        creer_fichier_demographics(nombre_lignes, nom_fichier)
        
        # Vérifier le fichier
        with open(nom_fichier, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f, delimiter=';')
            lignes = list(reader)
        
        if len(lignes) == nombre_lignes:
            print(f"✅ Fichier généré avec {len(lignes)} lignes")
            
            # Vérifier quelques lignes
            for i, ligne in enumerate(lignes[:3]):
                if not all(ligne.values()):
                    print(f"❌ Ligne {i+1} contient des champs vides")
                    return False
            
            print("✅ Contenu du fichier validé")
            
            # Nettoyer
            os.remove(nom_fichier)
            return True
        else:
            print(f"❌ Nombre de lignes incorrect: {len(lignes)} au lieu de {nombre_lignes}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur lors de la génération: {e}")
        return False

def main():
    """Exécute tous les tests"""
    print("🚀 Tests des améliorations du générateur de démographies")
    print("=" * 60)
    
    tests = [
        ("Génération complète", test_generation_complete),
        ("Cohérence sexe/civilité", test_coherence_sexe_civilite),
        ("Prénoms composés", test_prenoms_composes),
        ("Diversité civilités", test_diversite_civilites),
        ("Génération fichier", test_generation_fichier)
    ]
    
    resultats = []
    
    for nom_test, fonction_test in tests:
        try:
            resultat = fonction_test()
            resultats.append((nom_test, resultat))
        except Exception as e:
            print(f"❌ Erreur dans {nom_test}: {e}")
            resultats.append((nom_test, False))
    
    # Résumé
    print("\n" + "=" * 60)
    print("📊 RÉSUMÉ DES TESTS")
    print("=" * 60)
    
    succes = 0
    for nom_test, resultat in resultats:
        statut = "✅ RÉUSSI" if resultat else "❌ ÉCHEC"
        print(f"   {nom_test}: {statut}")
        if resultat:
            succes += 1
    
    print(f"\n🎯 Score: {succes}/{len(tests)} tests réussis")
    
    if succes == len(tests):
        print("🎉 Toutes les améliorations fonctionnent parfaitement !")
    else:
        print("⚠️  Certaines améliorations nécessitent des ajustements")

if __name__ == "__main__":
    main()
