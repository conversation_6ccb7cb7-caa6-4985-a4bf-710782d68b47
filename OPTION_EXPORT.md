# 🚀 Nouvelle Option --export

## ✨ Modification Demandée

L'option `--export` a été ajoutée pour spécifier le nombre de lignes à exporter dans la commande de lancement.

## 🔧 Changements Apportés

### 1. **Nouvelle Syntaxe Principale**
```bash
python demographics_generator_parse.py --export 500
```

### 2. **Arguments Modifiés**
- **`--export NOMBRE`** : Nouvelle option principale (recommandée)
- **`-n/--nombre NOMBRE`** : Conservée pour rétrocompatibilité (dépréciée)
- **`-o/--output FICHIER`** : Nom du fichier de sortie (inchangé)
- **`--stats`** : Affichage des statistiques (inchangé)

### 3. **Logique de Priorité**
1. Si `--export` est fourni → utilise cette valeur
2. Si `--export` absent mais `-n` fourni → utilise `-n` avec avertissement
3. Si aucune option → affiche erreur avec exemple

## 📋 Exemples d'Utilisation

### ✅ **Syntaxe Recommandée**
```bash
# Génération simple
python demographics_generator_parse.py --export 500

# Avec fichier personnalisé
python demographics_generator_parse.py --export 1000 -o mon_fichier.csv

# Avec statistiques
python demographics_generator_parse.py --export 200 --stats

# Exemple complet
python demographics_generator_parse.py --export 5000 -o demographics_2024.csv --stats
```

### ⚠️ **Syntaxe Dépréciée (mais fonctionnelle)**
```bash
# Ancienne syntaxe (affiche un avertissement)
python demographics_generator_parse.py -n 500 -o fichier.csv
# Sortie: "ATTENTION: L'option -n/--nombre est dépréciée, utilisez --export à la place"
```

### ❌ **Erreurs**
```bash
# Sans spécifier le nombre
python demographics_generator_parse.py -o fichier.csv
# Sortie: "ERREUR: Vous devez spécifier le nombre de lignes avec --export"
#         "   Exemple: python demographics_generator_parse.py --export 500"
```

## 🔍 **Aide Mise à Jour**
```bash
python demographics_generator_parse.py --help
```

```
usage: demographics_generator_parse.py [-h] --export EXPORT [-o OUTPUT] [--stats] [-n NOMBRE]

Générateur de démographies fictives pour tests HL7

options:
  -h, --help            show this help message and exit
  --export EXPORT       Nombre de démographies à exporter (recommandé)
  -o OUTPUT, --output OUTPUT
                        Nom du fichier de sortie (défaut: demographics.csv)
  --stats               Afficher les statistiques après génération
  -n NOMBRE, --nombre NOMBRE
                        Nombre de démographies à générer (déprécié, utilisez --export)
```

## ✅ **Tests de Validation**

### Test 1: Nouvelle Syntaxe
```bash
python demographics_generator_parse.py --export 100 -o test.csv
# ✅ Fonctionne parfaitement
```

### Test 2: Rétrocompatibilité
```bash
python demographics_generator_parse.py -n 50 -o test.csv
# ✅ Fonctionne avec avertissement
```

### Test 3: Gestion d'Erreur
```bash
python demographics_generator_parse.py -o test.csv
# ✅ Erreur claire avec exemple
```

### Test 4: Avec Statistiques
```bash
python demographics_generator_parse.py --export 200 --stats
# ✅ Génération + statistiques complètes
```

## 🎯 **Avantages**

1. **Syntaxe Claire** : `--export` est plus explicite que `-n`
2. **Rétrocompatibilité** : Les anciens scripts continuent de fonctionner
3. **Messages Informatifs** : Avertissements et erreurs clairs
4. **Cohérence** : Suit les conventions modernes d'arguments CLI

## 🔄 **Migration Recommandée**

### Ancien Code
```bash
python demographics_generator_parse.py -n 1000 -o data.csv --stats
```

### Nouveau Code
```bash
python demographics_generator_parse.py --export 1000 -o data.csv --stats
```

## 📊 **Résultats de Test**

Tous les tests manuels ont été validés :
- ✅ Option `--export` fonctionne
- ✅ Rétrocompatibilité `-n` maintenue
- ✅ Messages d'erreur appropriés
- ✅ Aide mise à jour
- ✅ Statistiques fonctionnelles

## 🎉 **Conclusion**

L'option `--export` est maintenant opérationnelle et constitue la méthode recommandée pour spécifier le nombre de démographies à générer. La transition est transparente grâce à la rétrocompatibilité maintenue.

---

*Utilisez désormais `--export` pour une syntaxe plus claire et moderne !*
