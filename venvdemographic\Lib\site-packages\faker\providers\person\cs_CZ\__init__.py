from collections import OrderedDict

from .. import Provider as PersonProvider


class Provider(PersonProvider):
    formats_female = OrderedDict(
        (
            ("{{first_name_female}} {{last_name_female}}", 0.97),
            ("{{prefix_female}} {{first_name_female}} {{last_name_female}}", 0.015),
            ("{{first_name_female}} {{last_name_female}} {{suffix}}", 0.02),
            (
                "{{prefix_female}} {{first_name_female}} {{last_name_female}} {{suffix}}",
                0.005,
            ),
        )
    )

    formats_male = OrderedDict(
        (
            ("{{first_name_male}} {{last_name_male}}", 0.97),
            ("{{prefix_male}} {{first_name_male}} {{last_name_male}}", 0.015),
            ("{{first_name_male}} {{last_name_male}} {{suffix}}", 0.02),
            (
                "{{prefix_male}} {{first_name_male}} {{last_name_male}} {{suffix}}",
                0.005,
            ),
        )
    )

    formats = formats_male.copy()
    formats.update(formats_female)

    # Names from
    # https://cs.wikipedia.org/wiki/Jmeniny_v_%C4%8Cesku

    first_names_male = (
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON>",
        "<PERSON>",
        "<PERSON>",
        "Dobroslav",
        "Dominik",
        "Drahoslav",
        "Dušan",
        "Eduard",
        "Emil",
        "Erik",
        "Ferdinand",
        "Felix",
        "Filip",
        "František",
        "Horymír",
        "Hubert",
        "Hynek",
        "Ignác",
        "Igor",
        "Ivan",
        "Ivo",
        "Jakub",
        "Jan",
        "Jaromír",
        "Jaroslav",
        "Jindřich",
        "Jiří",
        "Jonáš",
        "Josef",
        "Jozef",
        "Ján",
        "Kamil",
        "Karel",
        "Klement",
        "Kryštof",
        "Ladislav",
        "Leoš",
        "Libor",
        "Lubomír",
        "Luboš",
        "Ludvík",
        "Luděk",
        "Lukáš",
        "Lumír",
        "Marcel",
        "Marek",
        "Marian",
        "Martin",
        "Matouš",
        "Matyáš",
        "Matěj",
        "Michael",
        "Michal",
        "Mikuláš",
        "Milan",
        "Miloslav",
        "Miloš",
        "Miroslav",
        "Mojmír",
        "Norbert",
        "Oldřich",
        "Ondřej",
        "Otakar",
        "Oto",
        "Patrik",
        "Pavel",
        "Peter",
        "Petr",
        "Přemysl",
        "Radek",
        "Radim",
        "Radomír",
        "Radoslav",
        "Radovan",
        "René",
        "Richard",
        "Robert",
        "Robin",
        "Roman",
        "Rostislav",
        "Rudolf",
        "Samuel",
        "Stanislav",
        "Slavomír",
        "Svatopluk",
        "Svatoslav",
        "Šimon",
        "Tadeáš",
        "Teodor",
        "Tomáš",
        "Vasyl",
        "Viktor",
        "Vilém",
        "Vladimír",
        "Vladislav",
        "Vlasta",
        "Vlastimil",
        "Vojtěch",
        "Vratislav",
        "Václav",
        "Vít",
        "Vítězslav",
        "Zbyněk",
        "Zdeněk",
        "Zikmund",
        "Šimon",
        "Štefan",
        "Štěpán",
    )

    # Names from
    # https://cs.wikipedia.org/wiki/Jmeniny_v_%C4%8Cesku

    first_names_female = (
        "Adéla",
        "Agáta",
        "Alena",
        "Alexandra",
        "Alice",
        "Alžběta",
        "Anastázie",
        "Anděla",
        "Andrea",
        "Aneta",
        "Anežka",
        "Anna",
        "Apolena",
        "Barbora",
        "Blanka",
        "Blažena",
        "Bohumila",
        "Božena",
        "Dagmar",
        "Dana",
        "Daniela",
        "Danuše",
        "Denisa",
        "Dita",
        "Dominika",
        "Dorota",
        "Drahomíra",
        "Drahoslava",
        "Edita",
        "Elena",
        "Eliška",
        "Ema",
        "Emilie",
        "Erika",
        "Ester",
        "Eva",
        "Evelína",
        "Františka",
        "Gabriela",
        "Hana",
        "Helena",
        "Hedvika",
        "Ilona",
        "Irena",
        "Iva",
        "Ivana",
        "Iveta",
        "Ivona",
        "Jana",
        "Jarmila",
        "Jaroslava",
        "Jindřiška",
        "Jitka",
        "Jiřina",
        "Johana",
        "Jolana",
        "Judita",
        "Julie",
        "Kamila",
        "Karolína",
        "Kateřina",
        "Klaudie",
        "Klára",
        "Kristina",
        "Kristýna",
        "Květa",
        "Květoslava",
        "Lada",
        "Ladislava",
        "Lenka",
        "Leona",
        "Libuše",
        "Linda",
        "Lucie",
        "Ludmila",
        "Lýdie",
        "Magdalena",
        "Magdaléna",
        "Mahulena",
        "Marcela",
        "Marie",
        "Markéta",
        "Marta",
        "Martina",
        "Michaela",
        "Milada",
        "Milena",
        "Miloslava",
        "Miluše",
        "Miroslava",
        "Monika",
        "Mária",
        "Naděžda",
        "Natálie",
        "Nela",
        "Nina",
        "Nikol",
        "Nikola",
        "Nora",
        "Olivie",
        "Olga",
        "Otýlie",
        "Patricie",
        "Pavla",
        "Pavlína",
        "Petra",
        "Radka",
        "Renata",
        "Renáta",
        "Romana",
        "Růžena",
        "Sabina",
        "Simona",
        "Silvie",
        "Slavěna",
        "Soňa",
        "Stanislava",
        "Světlana",
        "Sára",
        "Šárka",
        "Štěpánka",
        "Tereza",
        "Vanda",
        "Vendula",
        "Veronika",
        "Věra",
        "Viktorie",
        "Vilma",
        "Vladimíra",
        "Vlasta",
        "Věra",
        "Zdenka",
        "Zdeňka",
        "Zora",
        "Zuzana",
        "Štěpánka",
        "Šárka",
        "Zdislava",
        "Žaneta",
        "Žofie",
    )

    first_names = first_names_male + first_names_female

    # Last names from
    # https://cs.wikipedia.org/wiki/Seznam_nej%C4%8Detn%C4%9Bj%C5%A1%C3%ADch_p%C5%99%C3%ADjmen%C3%AD_v_%C4%8Cesku

    last_names_male = (
        "Bárta",
        "Bartoš",
        "Bednář",
        "Beneš",
        "Beran",
        "Beránek",
        "Bílek",
        "Bláha",
        "Blažek",
        "Brož",
        "Bureš",
        "Čech",
        "Čermák",
        "Černý",
        "Doležal",
        "Dostál",
        "Dušek",
        "Dvořák",
        "Fiala",
        "Fišer",
        "Hájek",
        "Havel",
        "Havlíček",
        "Holub",
        "Horáček",
        "Horák",
        "Horváth",
        "Hrubý",
        "Hruška",
        "Janda",
        "Jaroš",
        "Jelínek",
        "Ježek",
        "Kadlec",
        "Kašpar",
        "Kolář",
        "Konečný",
        "Kopecký",
        "Kovář",
        "Král",
        "Kratochvíl",
        "Kraus",
        "Kříž",
        "Kubíček",
        "Kučera",
        "Liška",
        "Mach",
        "Macháček",
        "Malý",
        "Marek",
        "Mareš",
        "Mašek",
        "Matějka",
        "Matoušek",
        "Moravec",
        "Müller",
        "Musil",
        "Navrátil",
        "Němec",
        "Němeček",
        "Novák",
        "Novotný",
        "Pavlíček",
        "Pavlík",
        "Pokorný",
        "Polák",
        "Pospíšil",
        "Procházka",
        "Prokop",
        "Růžička",
        "Říha",
        "Sedláček",
        "Sedlák",
        "Slavík",
        "Soukup",
        "Staněk",
        "Stejskal",
        "Strnad",
        "Svoboda",
        "Sýkora",
        "Ševčík",
        "Šimek",
        "Šmíd",
        "Šťastný",
        "Štěpánek",
        "Švec",
        "Tesař",
        "Tichý",
        "Toman",
        "Tůma",
        "Urban",
        "Vacek",
        "Valenta",
        "Vaněk",
        "Vávra",
        "Veselý",
        "Vítek",
        "Vlček",
        "Zeman",
        "Žák",
    )

    # Last names from
    # https://cs.wikipedia.org/wiki/Seznam_nej%C4%8Detn%C4%9Bj%C5%A1%C3%ADch_p%C5%99%C3%ADjmen%C3%AD_v_%C4%8Cesku

    last_names_female = (
        "Bartošová",
        "Bártová",
        "Bednářová",
        "Benešová",
        "Beránková",
        "Beranová",
        "Bílková",
        "Bláhová",
        "Blažková",
        "Brožová",
        "Burešová",
        "Čechová",
        "Čermáková",
        "Černá",
        "Doležalová",
        "Dostálová",
        "Dušková",
        "Dvořáková",
        "Fialová",
        "Fišerová",
        "Hájková",
        "Havlíčková",
        "Holubová",
        "Horáčková",
        "Horáková",
        "Horváthová",
        "Hrubá",
        "Hrušková",
        "Jandová",
        "Janečková",
        "Jarošová",
        "Jelínková",
        "Ježková",
        "Kadlecová",
        "Kašparová",
        "Kolářová",
        "Konečná",
        "Kopecká",
        "Kovářová",
        "Králová",
        "Kratochvílová",
        "Krausová",
        "Krejčová",
        "Křížová",
        "Kubíčková",
        "Kučerová",
        "Lišková",
        "Macháčková",
        "Machová",
        "Malá",
        "Marešová",
        "Marková",
        "Mašková",
        "Matějková",
        "Matoušková",
        "Moravcová",
        "Müllerová",
        "Musilová",
        "Navrátilová",
        "Němcová",
        "Němečková",
        "Nováková",
        "Novotná",
        "Pavlíková",
        "Pešková",
        "Petrová",
        "Pokorná",
        "Poláková",
        "Pospíšilová",
        "Procházková",
        "Růžičková",
        "Říhová",
        "Sedláčková",
        "Sedláková",
        "Slavíková",
        "Soukupová",
        "Staňková",
        "Stejskalová",
        "Strnadová",
        "Svobodová",
        "Sýkorová",
        "Ševčíková",
        "Šimková",
        "Šmídová",
        "Šťastná",
        "Štěpánková",
        "Švecová",
        "Tichá",
        "Tomanová",
        "Tůmová",
        "Urbanová",
        "Vacková",
        "Valentová",
        "Vaňková",
        "Vávrová",
        "Veselá",
        "Vítková",
        "Vlčková",
        "Zemanová",
        "Žáková",
    )

    last_names = last_names_male + last_names_female

    # Degrees from
    # https://cs.wikipedia.org/wiki/Akademick%C3%BD_titul
    # https://eprehledy.cz/ceske_tituly.php

    degrees = ("JUDr.", "Ing.", "Bc.", "Mgr.", "MUDr.", "RNDr.", "Ing. arch.", "MVDr.", "PhDr.")

    prefixes_male = ("pan",) + degrees

    prefixes_female = ("paní", "slečna") + degrees

    suffixes = (
        "CSc.",
        "DiS.",
        "Ph.D.",
        "Th.D.",
        "DSc.",
    )
