from .. import Provider as PhoneNumberProvider


class Provider(PhoneNumberProvider):
    formats = (
        # Mobile
        # Mci
        "+98 91# ### ####",
        "091# ### ####",
        "+98 990 ### ####",
        "0990 ### ####",
        "+98 991 ### ####",
        "0991 ### ####",
        # Rightel Mobile prefixes
        "+98 920 ### ####",
        "0920 ### ####",
        "+98 921 ### ####",
        "0921 ### ####",
        "+98 922 ### ####",
        "0922 ### ####",
        # Samantel Mobile prefixes
        "+98 999 ### ####",
        "0999 ### ####",
        # Mtn and Talia
        "+98 93# ### ####",
        "093# ### ####",
        "+98 901 ### ####",
        "0901 ### ####",
        "+98 902 ### ####",
        "902 ### ####",
        "+98 903 ### ####",
        "0903 ### ####",
        "+98 905 ### ####",
        "0905 ### ####",
        # Land lines,
        # https://en.wikipedia.org/wiki/List_of_dialling_codes_in_Iran
        "+98 21 #### ####",
        "021 #### ####",
        "+98 26 #### ####",
        "026 #### ####",
        "+98 25 #### ####",
        "025 #### ####",
        "+98 86 #### ####",
        "086 #### ####",
        "+98 24 #### ####",
        "024 #### ####",
        "+98 23 #### ####",
        "023 #### ####",
        "+98 81 #### ####",
        "081 #### ####",
        "+98 28 #### ####",
        "028 #### ####",
        "+98 31 #### ####",
        "031 #### ####",
        "+98 44 #### ####",
        "044 #### ####",
        "+98 11 #### ####",
        "011 #### ####",
        "+98 74 #### ####",
        "074 #### ####",
        "+98 83 #### ####",
        "083 #### ####",
        "+98 51 #### ####",
        "051 #### ####",
        "+98 45 #### ####",
        "045 #### ####",
        "+98 17 #### ####",
        "017 #### ####",
        "+98 41 #### ####",
        "041 #### ####",
        "+98 54 #### ####",
        "054 #### ####",
        "+98 87 #### ####",
        "087 #### ####",
        "+98 71 #### ####",
        "071 #### ####",
        "+98 66 #### ####",
        "066 #### ####",
        "+98 34 #### ####",
        "034 #### ####",
        "+98 56 #### ####",
        "056 #### ####",
        "+98 13 #### ####",
        "013 #### ####",
        "+98 77 #### ####",
        "077 #### ####",
        "+98 76 #### ####",
        "076 #### ####",
        "+98 61 #### ####",
        "061 #### ####",
        "+98 38 #### ####",
        "038 #### ####",
        "+98 58 #### ####",
        "058 #### ####",
        "+98 35 #### ####",
        "035 #### ####",
        "+98 84 #### ####",
        "084 #### ####",
        "+98 ### #### ####",
        "0### #### ####",
    )
