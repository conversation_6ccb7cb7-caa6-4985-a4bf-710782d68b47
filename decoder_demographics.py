#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Décodeur de démographies optimisées
Convertit les index numériques en valeurs lisibles
"""

import csv
import argparse
import sys
import os

def charger_index_inverse(fichier_index, cle_id='id', cle_valeur=None):
    """
    Charge un fichier d'index et retourne un dictionnaire inverse (id -> valeur)
    """
    index_inverse = {}
    try:
        with open(fichier_index, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f, delimiter=';')
            for row in reader:
                if cle_valeur:
                    # Pour les communes : id -> (code_postal, ville)
                    if cle_valeur == 'commune':
                        index_inverse[int(row[cle_id])] = f"{row['code_postal']};{row['ville']}"
                    else:
                        index_inverse[int(row[cle_id])] = row[cle_valeur]
                else:
                    # Prendre la première colonne après l'id
                    colonnes = list(row.keys())
                    if len(colonnes) > 1:
                        index_inverse[int(row[cle_id])] = row[colonnes[1]]
        return index_inverse
    except FileNotFoundError:
        print(f"ATTENTION: Fichier {fichier_index} non trouvé")
        return {}
    except Exception as e:
        print(f"ERREUR lors du chargement de {fichier_index}: {e}")
        return {}

def decoder_fichier_demographics(fichier_entree, fichier_sortie=None):
    """
    Décode un fichier de démographies optimisé en version lisible
    """
    if fichier_sortie is None:
        base_name = os.path.splitext(fichier_entree)[0]
        fichier_sortie = f"{base_name}_decoded.csv"
    
    print(f"Décodage de {fichier_entree} vers {fichier_sortie}")
    
    # Chargement des index inverses
    print("Chargement des index de décodage...")
    civilites_decode = charger_index_inverse('civilites_index.csv', 'id', 'civilite')
    noms_decode = charger_index_inverse('noms_index.csv', 'id', 'nom')
    prenoms_decode = charger_index_inverse('prenoms_index.csv', 'id', 'prenom')
    communes_decode = charger_index_inverse('communes_index.csv', 'id', 'commune')
    
    # Affichage des index chargés
    index_charges = []
    if civilites_decode:
        index_charges.append(f"civilités ({len(civilites_decode)})")
    if noms_decode:
        index_charges.append(f"noms ({len(noms_decode)})")
    if prenoms_decode:
        index_charges.append(f"prénoms ({len(prenoms_decode)})")
    if communes_decode:
        index_charges.append(f"communes ({len(communes_decode)})")
    
    if index_charges:
        print(f"✅ Index chargés: {', '.join(index_charges)}")
    else:
        print("⚠️  Aucun index chargé - le fichier sera copié sans décodage")
    
    # Lecture et décodage
    try:
        with open(fichier_entree, 'r', encoding='utf-8') as f_in:
            reader = csv.DictReader(f_in, delimiter=';')
            
            # Nouveaux en-têtes pour le fichier décodé
            headers_decoded = [
                'civilite',
                'sexe',
                'nom_naissance',
                'nom_usage',
                'prenom',
                'date_naissance',
                'code_postal',
                'ville',
                'numero_securite_sociale',
                'numero_ins'
            ]
            
            with open(fichier_sortie, 'w', newline='', encoding='utf-8') as f_out:
                writer = csv.DictWriter(f_out, fieldnames=headers_decoded, delimiter=';')
                writer.writeheader()
                
                lignes_traitees = 0
                for row in reader:
                    lignes_traitees += 1
                    if lignes_traitees % 10000 == 0:
                        print(f"Progression: {lignes_traitees} lignes traitées")
                    
                    # Décodage des valeurs
                    civilite = row.get('civilite', '')
                    if civilite.isdigit() and int(civilite) in civilites_decode:
                        civilite = civilites_decode[int(civilite)]
                    
                    nom_naissance = row.get('nom_naissance', '')
                    if nom_naissance.isdigit() and int(nom_naissance) in noms_decode:
                        nom_naissance = noms_decode[int(nom_naissance)]
                    
                    nom_usage = row.get('nom_usage', '')
                    if nom_usage.isdigit() and int(nom_usage) in noms_decode:
                        nom_usage = noms_decode[int(nom_usage)]
                    
                    prenom = row.get('prenom', '')
                    if prenom.isdigit() and int(prenom) in prenoms_decode:
                        prenom = prenoms_decode[int(prenom)]
                    
                    # Décodage de la commune
                    commune = row.get('commune', '')
                    code_postal = ''
                    ville = ''
                    
                    if commune.isdigit() and int(commune) in communes_decode:
                        # Décodage depuis l'index
                        commune_decoded = communes_decode[int(commune)]
                        if ';' in commune_decoded:
                            code_postal, ville = commune_decoded.split(';', 1)
                        else:
                            code_postal = commune_decoded
                            ville = commune_decoded
                    elif ';' in commune:
                        # Déjà au format code_postal;ville
                        code_postal, ville = commune.split(';', 1)
                    else:
                        # Format inconnu
                        code_postal = commune
                        ville = commune
                    
                    # Écriture de la ligne décodée
                    writer.writerow({
                        'civilite': civilite,
                        'sexe': row.get('sexe', ''),
                        'nom_naissance': nom_naissance,
                        'nom_usage': nom_usage,
                        'prenom': prenom,
                        'date_naissance': row.get('date_naissance', ''),
                        'code_postal': code_postal,
                        'ville': ville,
                        'numero_securite_sociale': row.get('numero_securite_sociale', ''),
                        'numero_ins': row.get('numero_ins', '')
                    })
                
                print(f"✅ Décodage terminé: {lignes_traitees} lignes traitées")
                print(f"📄 Fichier décodé: {fichier_sortie}")
    
    except FileNotFoundError:
        print(f"ERREUR: Fichier {fichier_entree} non trouvé")
        sys.exit(1)
    except Exception as e:
        print(f"ERREUR lors du décodage: {e}")
        sys.exit(1)

def main():
    parser = argparse.ArgumentParser(
        description="Décodeur de démographies optimisées - convertit les index en valeurs lisibles"
    )
    parser.add_argument(
        'fichier_entree',
        help="Fichier CSV d'entrée avec les index"
    )
    parser.add_argument(
        '-o', '--output',
        help="Fichier CSV de sortie (défaut: [entrée]_decoded.csv)"
    )
    
    args = parser.parse_args()
    
    if not os.path.exists(args.fichier_entree):
        print(f"ERREUR: Le fichier {args.fichier_entree} n'existe pas")
        sys.exit(1)
    
    try:
        decoder_fichier_demographics(args.fichier_entree, args.output)
    except KeyboardInterrupt:
        print("\nDécodage interrompu par l'utilisateur")
        sys.exit(1)
    except Exception as e:
        print(f"ERREUR: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
