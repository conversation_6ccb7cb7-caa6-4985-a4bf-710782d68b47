#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test des différentes optimisations de dates
"""

from datetime import datetime, timedelta
import random

def test_formats_dates():
    """
    Compare différents formats de dates pour l'optimisation
    """
    # Génération de 1000 dates aléatoires
    dates = []
    for _ in range(1000):
        year = random.randint(1920, 2024)
        month = random.randint(1, 12)
        day = random.randint(1, 28)  # Pour éviter les problèmes de jours invalides
        dates.append(datetime(year, month, day))
    
    print("🗓️  Test des formats de dates sur 1000 échantillons")
    print("=" * 60)
    
    # Format 1: dd/mm/yyyy (original)
    format1 = [d.strftime('%d/%m/%Y') for d in dates]
    taille1 = sum(len(s) for s in format1)
    print(f"Format dd/mm/yyyy    : {taille1:,} caractères (exemple: {format1[0]})")
    
    # Format 2: yyyymmdd (votre suggestion)
    format2 = [d.strftime('%Y%m%d') for d in dates]
    taille2 = sum(len(s) for s in format2)
    gain2 = ((taille1 - taille2) / taille1) * 100
    print(f"Format yyyymmdd     : {taille2:,} caractères (exemple: {format2[0]}) - Gain: {gain2:.1f}%")
    
    # Format 3: Nombre de jours depuis une date de référence (1900-01-01)
    reference = datetime(1900, 1, 1)
    format3 = [str((d - reference).days) for d in dates]
    taille3 = sum(len(s) for s in format3)
    gain3 = ((taille1 - taille3) / taille1) * 100
    print(f"Format jours depuis 1900: {taille3:,} caractères (exemple: {format3[0]}) - Gain: {gain3:.1f}%")
    
    # Format 4: Timestamp Unix (secondes depuis 1970) - seulement pour dates >= 1970
    format4 = []
    for d in dates:
        if d.year >= 1970:
            format4.append(str(int(d.timestamp())))
        else:
            # Pour les dates < 1970, utiliser un format alternatif
            format4.append(str((d - datetime(1970, 1, 1)).days))
    taille4 = sum(len(s) for s in format4)
    gain4 = ((taille1 - taille4) / taille1) * 100
    print(f"Format timestamp    : {taille4:,} caractères (exemple: {format4[0]}) - Gain: {gain4:.1f}%")
    
    # Format 5: Encodage compact personnalisé (AAMMJJ où AA = année - 1900)
    format5 = []
    for d in dates:
        aa = d.year - 1900  # 0-124 pour 1900-2024
        mm = d.month
        jj = d.day
        # Format: AAMMJJ avec AA sur 2 chiffres, MM et JJ sur 2 chiffres
        compact = f"{aa:02d}{mm:02d}{jj:02d}"
        format5.append(compact)
    taille5 = sum(len(s) for s in format5)
    gain5 = ((taille1 - taille5) / taille1) * 100
    print(f"Format compact AAMMJJ: {taille5:,} caractères (exemple: {format5[0]}) - Gain: {gain5:.1f}%")
    
    print("\n📊 Résumé des gains pour 200,000 démographies:")
    print("=" * 60)
    
    # Projection pour 200,000 lignes
    facteur = 200
    
    print(f"dd/mm/yyyy          : {taille1 * facteur:,} caractères")
    print(f"yyyymmdd            : {taille2 * facteur:,} caractères (économie: {(taille1-taille2)*facteur:,})")
    print(f"Jours depuis 1900   : {taille3 * facteur:,} caractères (économie: {(taille1-taille3)*facteur:,})")
    print(f"Timestamp           : {taille4 * facteur:,} caractères (économie: {(taille1-taille4)*facteur:,})")
    print(f"Compact AAMMJJ      : {taille5 * facteur:,} caractères (économie: {(taille1-taille5)*facteur:,})")
    
    print("\n💡 Recommandations:")
    print("=" * 60)
    print("1. ✅ yyyymmdd (déjà implémenté) - Simple et efficace")
    print("2. 🚀 Jours depuis 1900 - Meilleur gain, mais nécessite décodage")
    print("3. ⚡ Compact AAMMJJ - Bon compromis lisibilité/gain")
    print("4. 🔧 Timestamp - Très compact mais moins lisible")

def decoder_jours_depuis_1900(jours):
    """
    Décode un nombre de jours depuis le 1er janvier 1900
    """
    reference = datetime(1900, 1, 1)
    date_result = reference + timedelta(days=int(jours))
    return date_result.strftime('%d/%m/%Y')

def decoder_compact_aammjj(compact):
    """
    Décode le format compact AAMMJJ
    """
    if len(compact) != 6:
        return compact
    
    aa = int(compact[:2]) + 1900
    mm = int(compact[2:4])
    jj = int(compact[4:6])
    
    return f"{jj:02d}/{mm:02d}/{aa}"

if __name__ == "__main__":
    test_formats_dates()
