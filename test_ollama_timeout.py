#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de test pour vérifier les corrections du timeout Ollama
"""

import sys
import os

# Ajouter le répertoire courant au path pour importer le module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from demographics_generator_IA import OllamaClient, check_ollama_setup

def test_ollama_connection():
    """Test de connexion avec différents timeouts"""
    print("🧪 Test de connexion Ollama avec timeout amélioré")
    print("=" * 50)
    
    # Test avec timeout par défaut (120s)
    print("\n1. Test avec timeout par défaut (120s)")
    client = check_ollama_setup()
    
    if not client:
        print("❌ Impossible de se connecter à Ollama")
        return False
    
    # Test de génération simple
    print("\n2. Test de génération simple")
    test_prompt = "Générez un prénom français: "
    
    try:
        result = client.generate(test_prompt, temperature=0.5)
        if result:
            print(f"✅ Test réussi! Résultat: '{result[:50]}...'")
            return True
        else:
            print("❌ Aucun résultat généré")
            return False
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        return False

def test_different_timeouts():
    """Test avec différents timeouts"""
    print("\n3. Test avec différents timeouts")
    
    timeouts = [30, 60, 120, 180]
    
    for timeout in timeouts:
        print(f"\n   Test avec timeout {timeout}s...")
        client = OllamaClient(timeout=timeout)
        
        if client.is_available():
            print(f"   ✅ Connexion OK avec timeout {timeout}s")
        else:
            print(f"   ❌ Connexion échouée avec timeout {timeout}s")

if __name__ == "__main__":
    print("🚀 Démarrage des tests de timeout Ollama")
    
    success = test_ollama_connection()
    test_different_timeouts()
    
    if success:
        print("\n🎉 Tous les tests sont passés!")
        print("\n💡 Conseils d'utilisation:")
        print("   - Pour des modèles lents, utilisez: --timeout 300")
        print("   - Pour des lots plus petits: --batch-size 5")
        print("   - Pour des modèles rapides: gemma2:2b")
    else:
        print("\n⚠️  Certains tests ont échoué")
        print("\n🔧 Vérifications à faire:")
        print("   1. Ollama est-il démarré? (ollama serve)")
        print("   2. Un modèle est-il installé? (ollama list)")
        print("   3. Le port 11434 est-il libre?")
